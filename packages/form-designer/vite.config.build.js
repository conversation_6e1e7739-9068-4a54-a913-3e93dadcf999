import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJSX from '@vitejs/plugin-vue-jsx'
import banner from 'vite-plugin-banner'
import cssnano from 'cssnano'
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js'

const pkg = {
    name: 'form-designer',
    version: '1.0.0',
    author: 'form-designer team',
    license: 'MIT'
}

function getBanner(banner, pkg) {
    if (!banner || typeof banner === 'string') {
        return banner || '';
    }

    banner = {...pkg, ...(banner === true ? {} : banner)};

    const author = banner.author
    const license = banner.license || '';
    
    return (
        '/*!\n' +
        ' * FormDesigner 高性能可视化表单设计器\n' +
        ` * ${banner.name} v${banner.version}\n` +
        ` * (c) ${author || ''}\n` +
        (license && ` * Released under the ${license} License.\n`) +
        ' */'
    );
}

const __banner__ = {
    author: `2024-${new Date().getFullYear()} ${pkg.author}`,
    license: pkg.license,
    name: pkg.name,
    version: pkg.version
}

// https://vitejs.dev/config/
export default defineConfig({
    build: {
        lib: {
            entry: 'src/index.js',
            name: 'FormDesigner',
            fileName: format => `index.${format}.js`,
        },
        rollupOptions: {
            output: {
                exports: 'named',
                globals: {
                    vue: 'Vue',
                    'element-plus': 'ElementPlus',
                }
            },
            external: [
                'vue',
                'element-plus',
                '@form-create/element-ui'
            ],
        },
        // 启用压缩和优化
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: true,
                drop_debugger: true,
            },
        },
        // 启用 gzip 大小报告
        reportCompressedSize: true,
        // 代码分割优化
        chunkSizeWarningLimit: 1000,
    },
    css: {
        postcss: {
            plugins: [
                cssnano({
                    preset: ['default', {
                        discardComments: {
                            removeAll: true,
                        },
                    }]
                })
            ]
        }
    },
    // 性能优化配置
    optimizeDeps: {
        include: [
            'vue',
            'element-plus',
            'vuedraggable',
            '@form-create/element-ui'
        ]
    },
    plugins: [
        vue({
            // 启用生产优化
            template: {
                compilerOptions: {
                    isCustomElement: (tag) => tag.startsWith('fc-')
                }
            }
        }), 
        vueJSX(), 
        banner(getBanner(__banner__)),
        cssInjectedByJsPlugin({
            // CSS 注入优化
            styleId: 'form-designer-styles'
        })
    ]
})
